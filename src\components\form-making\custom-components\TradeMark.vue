<script lang="ts" setup>
import { onMounted, onUnmounted, ref, inject } from 'vue'
import type { Ref } from 'vue'
import aicService from '@/service/aicService'
import type { IGetModelCategoryResponse } from '@/types/company'
import type { ITradeMarkResponseItem } from '@/types/model'
import eventBus from '@/utils/eventBus'
const props = defineProps<{
    modelItem: IGetModelCategoryResponse
}>()

const emits = defineEmits(['updateTotal', 'updateBuyStatus'])
const tableLoading = ref(false)
const pageInfo = ref({
    page: 1,
    pageSize: 10,
    total: 0,
})
const lockStatus = ref(false)
const socialCreditCode: Ref<string> = inject('socialCreditCode', ref(''))
const tableData = ref<ITradeMarkResponseItem[]>([])
const columns = [
    { label: '商标', prop: 'MARKIMGOSS' },
    { label: '商标名称', prop: 'MARKNAME' },
    { label: '注册号', prop: 'APPLYNO' },
    { label: '申请时间', prop: 'APPLYDATE' },
    { label: '商标类型编号', prop: 'markTypeStr' },
    { label: '状态', prop: 'STATUS' },
    { label: '专用权结束时间', prop: 'USERIGHTDATEEND' },
]
const getModelData = () => {
    tableLoading.value = true
    aicService
        .gsInfo({
            socialCreditCode: socialCreditCode.value,
            modelName: props.modelItem.name,
            page: pageInfo.value.page,
            pageSize: pageInfo.value.pageSize,
        })
        .then((res) => {
            const { isLock, total, items } = res
            lockStatus.value = isLock === 1 ? true : false
            tableData.value = items as ITradeMarkResponseItem[]
            pageInfo.value.total = total
            emits('updateTotal', total)
            tableLoading.value = false
        })
}
onMounted(() => {
    getModelData()
    // 监听刷新事件
    eventBus.$on('refreshCustomComponentData', getModelData)
})

onUnmounted(() => {
    eventBus.$off('refreshCustomComponentData', getModelData)
})

defineExpose({
    getModelData
})
</script>
<template>
    <el-table
        v-loading="tableLoading"
        :data="tableData"
        tooltip-effect="dark"
        border
        table-layout="fixed"
        fit
        :header-cell-style="{
            background: '#ECF5FF',
        }"
        size="large"
        empty-text="暂无数据"
    >
        <!-- <el-table-column type="index" label="序号" width="80" align="center" header-align="center"></el-table-column> -->
        <el-table-column
            align="center"
            header-align="center"
            v-for="(item, index) in columns"
            :key="index"
            :label="item.label"
            :prop="item.prop"
        >
            <template #default="{ row }">
                <div v-if="item.prop === 'MARKIMGOSS'">
                    <div v-if="row.MARKIMGOSS" class="pointer">
                        <TradeMarkDetailDialog :row="row"></TradeMarkDetailDialog>
                    </div>
                    <div v-else>-</div>
                </div>
                <div v-else-if="item.prop === 'MARKNAME'">
                    <div v-if="row.MARKNAME" class="pointer">
                        <TradeMarkDetailDialog :row="row" :type="'MARKNAME'"></TradeMarkDetailDialog>
                    </div>
                    <div v-else>-</div>
                </div>

                <div v-else>
                    {{ row[item.prop] || '-' }}
                </div>
            </template>
        </el-table-column>
    </el-table>
    <div class="display-flex top-bottom-center margin-top-20 justify-flex-end">
        <el-pagination
            :hide-on-single-page="true"
            v-model:currentPage="pageInfo.page"
            v-model:page-size="pageInfo.pageSize"
            layout="total, prev, pager, next"
            :total="pageInfo.total"
            @current-change="getModelData()"
        />
    </div>
</template>
<style scoped lang="scss"></style>
