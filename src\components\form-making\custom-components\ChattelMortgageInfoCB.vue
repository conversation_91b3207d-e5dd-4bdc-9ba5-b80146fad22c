<script lang="ts" setup>
import { onMounted, ref, inject } from 'vue'
import type { Ref } from 'vue'
import aicService from '@/service/aicService'
import type { IGetModelCategoryResponse } from '@/types/company'
import type { IChattelMortgageInfoCBResponseItem } from '@/types/model'
import ChattelMortgageInfoCBDetailDialog from '@/components/search/custom-modal/components/ChattelMortgageInfoCBDetailDialog.vue'
const props = defineProps<{
    modelItem: IGetModelCategoryResponse
}>()
const tableLoading = ref(false)
const pageInfo = ref({
    page: 1,
    pageSize: 10,
    total: 0,
})

const emits = defineEmits(['updateTotal'])
//计算表格的index
// const indexFilter = (index: number) => {
//     return (pageInfo.value.page - 1) * pageInfo.value.pageSize + index + 1
// }

const socialCreditCode: Ref<string> = inject('socialCreditCode', ref(''))
const tableData = ref<IChattelMortgageInfoCBResponseItem[]>([])
const columns = [
    { label: '登记编号', prop: 'registNo' },
    { label: '登记机关', prop: 'organName' },
    { label: '登记日期', prop: 'registDate' },
    { label: '被担保债权数额', prop: 'guaranteeBondDesc' },
    { label: '状态', prop: 'status' },
]
const getModelData = () => {
    tableLoading.value = true
    aicService
        .gsInfo({
            socialCreditCode: socialCreditCode.value,
            modelName: props.modelItem.name,
            page: pageInfo.value.page,
            pageSize: pageInfo.value.pageSize,
        })
        .then((res) => {
            const { total, items } = res
            tableData.value = items as IChattelMortgageInfoCBResponseItem[]
            pageInfo.value.total = total
            emits('updateTotal', total)
            if (pageInfo.value.page !== 1) {
                jumpHref()
            }
        }).finally(() => {
            tableLoading.value = false
        })
}

const jumpHref = () => {
    document.getElementById(`model-${props.modelItem.name}`)?.scrollIntoView({
        behavior: 'smooth',
        inline: 'nearest',
    })
}

onMounted(() => {
    getModelData()
})

defineExpose({
    getModelData
})
</script>
<template>
    <el-table
        v-loading="tableLoading"
        :data="tableData"
        tooltip-effect="dark"
        border
        table-layout="fixed"
        fit
        :header-cell-style="{
            background: '#ECF5FF',
        }"
        size="large"
        empty-text="暂无数据"
    >
        <!-- <el-table-column type="index" label="序号" width="80" align="center" :index="indexFilter" header-align="center"></el-table-column> -->
        <el-table-column
            align="center"
            header-align="center"
            v-for="(item, index) in columns"
            :key="index"
            :label="item.label"
            :prop="item.prop"
        >
            <template #default="{ row }">
                <div>
                    {{ row[item.prop] || '-' }}
                </div>
            </template>
        </el-table-column>
        <el-table-column label="内容" width="90" align="center" header-align="center">
            <template #default="{ row }">
                <ChattelMortgageInfoCBDetailDialog :row="row" />
            </template>
        </el-table-column>
    </el-table>
    <el-pagination
        v-if="pageInfo.total > 0"
        class="flex justify-end"
        v-model:current-page="pageInfo.page"
        v-model:page-size="pageInfo.pageSize"
        :page-sizes="[10]"
        layout="total, prev, pager, next"
        :total="pageInfo.total"
        @change="getModelData"
    />
</template>
<style scoped lang="scss"></style>
