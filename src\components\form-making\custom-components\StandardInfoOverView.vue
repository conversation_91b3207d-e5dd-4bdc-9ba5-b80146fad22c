<script lang="ts" setup>
import { onMounted, onUnmounted, ref, inject } from 'vue'
import type { Ref } from 'vue'
import aicService from '@/service/aicService'
import type { IGetModelCategoryResponse } from '@/types/company'
import type { IStandardInfoOverViewResponseItem } from '@/types/model'
import eventBus from '@/utils/eventBus'
import StandardInfoOverViewDetailDialog from '@/components/search/custom-modal/components/StandardInfoOverViewDetailDialog.vue'
const props = defineProps<{
    modelItem: IGetModelCategoryResponse
}>()

const emits = defineEmits(['updateTotal', 'updateLockStatus'])
const tableLoading = ref(false)
const pageInfo = ref({
    page: 1,
    pageSize: 10,
    total: 0,
})
const lockStatus = ref(false)
const socialCreditCode: Ref<string> = inject('socialCreditCode', ref(''))
const tableData = ref<IStandardInfoOverViewResponseItem[]>([])
const columns = [
    { label: '标准号', prop: 'standardNumber' },
    { label: '标准名称', prop: 'standardName' },
    { label: '标准级别', prop: 'standardRank' },
    { label: '标准性质', prop: 'standardProperty'},
    { label: '起草单位', prop: 'draftingUnits'},
    { label: '发布日期', prop: 'releaseDate' },
    { label: '标准状态', prop: 'standardState' },
]
const getModelData = () => {
    tableLoading.value = true
    aicService
        .gsInfo({
            socialCreditCode: socialCreditCode.value,
            modelName: props.modelItem.name,
        })
        .then((res) => {
            const { isLock, total, items } = res
            lockStatus.value = isLock === 1 ? true : false
            tableData.value = items as IStandardInfoOverViewResponseItem[]
            pageInfo.value.total = total
            emits('updateTotal', total)
            emits('updateLockStatus', lockStatus.value)
            tableLoading.value = false
        })
}
onMounted(() => {
    getModelData()
    // 监听刷新事件
    eventBus.$on('refreshCustomComponentData', getModelData)
})

onUnmounted(() => {
    eventBus.$off('refreshCustomComponentData', getModelData)
})

defineExpose({
    getModelData
})
</script>
<template>
    <div>
        <el-table
            v-loading="tableLoading"
            :data="tableData"
            tooltip-effect="dark"
            border
            table-layout="fixed"
            fit
            :header-cell-style="{
                background: '#ECF5FF',
            }"
            size="large"
            empty-text="暂无数据"
        >
            <!-- <el-table-column type="index" label="序号" width="80" align="center" header-align="center"></el-table-column> -->
            <el-table-column
                align="center"
                header-align="center"
                v-for="(item, index) in columns"
                :key="index"
                :label="item.label"
                :prop="item.prop"
            >
                <template #default="{ row }">
                    <div v-if="item.prop === 'standardName'">
                        <StandardInfoOverViewDetailDialog :row="row" />
                    </div>

                    <div v-else-if="item.prop === 'draftingUnits'">
                        <el-tooltip :content='row[item.prop].map((p:{name:string}) => p.name).join(";")'>
                            <span class="text-ellipsis width-100" v-for="(p, i) in row[item.prop].slice(0,1)" :key="i">
                                {{ p.name }};
                                <span v-if="row[item.prop].length > 1">...</span>
                            </span>
                        </el-tooltip>
                    </div>
                    <div v-else>
                        {{ row[item.prop] || '-' }}
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <div class="display-flex top-bottom-center margin-top-20 justify-flex-end">
            <el-pagination
                :hide-on-single-page="true"
                v-model:currentPage="pageInfo.page"
                v-model:page-size="pageInfo.pageSize"
                layout="total, prev, pager, next"
                :total="pageInfo.total"
                @current-change="getModelData()"
            />
        </div>
    </div>
</template>
<style scoped lang="scss">
.no-pay-item {
    background: url('@/assets/images/model-lock.jpg') no-repeat;
    width: 100%;
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    border: 3px solid var(--border-color);
}
</style>
