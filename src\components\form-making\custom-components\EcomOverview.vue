<script lang="ts" setup>
import { onMounted, onUnmounted, ref, reactive, inject } from 'vue'
import type { Ref } from 'vue'
import aicService from '@/service/aicService'
import type { IGetModelCategoryResponse } from '@/types/company'
import type { IEcomOverviewResponse } from '@/types/model'
import eventBus from '@/utils/eventBus'

const props = defineProps<{
    modelItem: IGetModelCategoryResponse
}>()

const tableLoading = ref(false)
const socialCreditCode: Ref<string> = inject('socialCreditCode', ref(''))
const modelData = reactive<IEcomOverviewResponse>({
    avgScore: '',
    esDate: '',
    mainBrand: [],
    mainBrandCount: '',
    platform: [],
    mainProduct: [],
    mainProductCount: '',
    productCategory: [],
    productCategoryCount: '',
    productCount: '',
    channelType: 0,
    isLock: 0,
})
const getModelData = () => {
    tableLoading.value = true
    aicService
        .gsInfo({
            socialCreditCode: socialCreditCode.value,
            modelName: props.modelItem.name,
        })
        .then((res) => {
            Object.assign(modelData, res)
            tableLoading.value = false
        })
}

onMounted(() => {
    getModelData()
    // 监听刷新事件
    eventBus.$on('refreshCustomComponentData', getModelData)
})

onUnmounted(() => {
    eventBus.$off('refreshCustomComponentData', getModelData)
})

defineExpose({
    getModelData
})
</script>

<template>
    <div class="font-14 color-two-grey">
        <div class="b-margin-24">
            <el-row>
                <el-col
                    :span="6"
                    class="all-padding-16"
                    style="background-color: #ECF5FF; border: 1px solid #EBEEF5"
                >电商店铺平均评分</el-col
                >
                <el-col :span="6" class="all-padding-16" style="border: 1px solid #EBEEF5"
                >{{ modelData.avgScore || '-' }}
                </el-col>
                <el-col
                    :span="6"
                    class="all-padding-16"
                    style="background-color: #ECF5FF; border: 1px solid #EBEEF5"
                >店铺创建时间
                </el-col>
                <el-col :span="6" class="all-padding-16" style="border: 1px solid #EBEEF5">{{
                    modelData?.esDate || '-'
                }}</el-col>
            </el-row>
            <el-row>
                <el-col
                    :span="6"
                    class="all-padding-16"
                    style="background-color: #ECF5FF; border: 1px solid #EBEEF5"
                >产品分类
                </el-col>
                <el-col :span="6" class="all-padding-16" style="border: 1px solid #EBEEF5">{{
                    modelData.productCategoryCount || '-'
                }}</el-col>

                <el-col
                    :span="6"
                    class="all-padding-16"
                    style="background-color: #ECF5FF; border: 1px solid #EBEEF5"
                >主营品牌
                </el-col>
                <el-col :span="6" class="all-padding-16" style="border: 1px solid #EBEEF5">{{
                    modelData.mainBrandCount || '-'
                }}</el-col>
            </el-row>
            <el-row>
                <el-col
                    :span="6"
                    class="all-padding-16"
                    style="background-color: #ECF5FF; border: 1px solid #EBEEF5"
                >店铺商品总数</el-col
                >
                <el-col :span="6" class="all-padding-16" style="border: 1px solid #EBEEF5">{{
                    modelData.productCount || '-'
                }}</el-col>
                <el-col
                    :span="6"
                    class="all-padding-16"
                    style="background-color: #ECF5FF; border: 1px solid #EBEEF5"
                >主营产品</el-col
                >
                <el-col :span="6" class="all-padding-16" style="border: 1px solid #EBEEF5">{{
                    modelData.mainProductCount || '-'
                }}</el-col>
            </el-row>
            <el-row>
                <el-col
                    :span="6"
                    class="all-padding-16"
                    style="background-color: #ECF5FF; border: 1px solid #EBEEF5"
                >上架平台</el-col
                >
                <el-col :span="18" class="all-padding-16" style="border: 1px solid #EBEEF5">{{
                    modelData?.platform.length ? modelData?.platform.join(',') : '-'
                }}</el-col>
            </el-row>
        </div>
    </div>
</template>
<style scoped lang="scss"></style>
