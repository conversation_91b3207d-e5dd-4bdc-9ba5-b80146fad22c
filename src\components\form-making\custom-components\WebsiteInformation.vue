<script lang="ts" setup>
import { onMounted, ref, inject } from 'vue'
import type { Ref } from 'vue'
import aicService from '@/service/aicService'
import type { IGetModelCategoryResponse } from '@/types/company'
import type { IWebsiteInformationResponse } from '@/types/model'
import WebsiteInformationDetailDialog from '@/components/search/custom-modal/components/WebsiteInformationDetailDialog.vue'
const props = defineProps<{
    modelItem: IGetModelCategoryResponse
}>()
const emits = defineEmits(['updateTotal', 'updateLockStatus'])
const tableLoading = ref(false)
const pageInfo = ref({
    page: 1,
    pageSize: 10,
    total: 0,
})
const lockStatus = ref(false)
const socialCreditCode: Ref<string> = inject('socialCreditCode', ref(''))
const tableData = ref<IWebsiteInformationResponse[]>([])
const columns = [
    { label: '网址', prop: 'SITEHOME' },
    { label: '网站名称', prop: 'SITENAME' },
    { label: '域名', prop: 'SITEDOMAIN' },
    { label: '网站备案/许可证号', prop: 'RECORDID' },
    { label: '审核时间', prop: 'CHECKDATE' },
    { label: '域名年龄', prop: 'domainAge' },
    { label: '到期时间', prop: 'domainExpireTime' },
    // { label: '公司名称', prop: 'COMPANYNAME' },
    // { label: '内容', prop: 'DETAIL' },
]
const getModelData = () => {
    tableLoading.value = true
    aicService
        .gsInfo({
            socialCreditCode: socialCreditCode.value,
            modelName: props.modelItem.name,
            page: pageInfo.value.page,
            pageSize: pageInfo.value.pageSize,
        })
        .then((res) => {
            const { isLock, total, items } = res
            lockStatus.value = isLock === 1 ? true : false
            tableData.value = items as IWebsiteInformationResponse[]
            pageInfo.value.total = total
            emits('updateTotal', total)
            emits('updateLockStatus', lockStatus.value)
            tableLoading.value = false
        })
}

defineExpose({
    getModelData,
})

onMounted(() => {
    getModelData()
})
const openWebSite = (val: string) => {
    // 自动补全协议
    let url = val.trim()
    if (!/^https?:\/\//i.test(url)) {
        url = 'https://' + url
    }

    // 可选：增加 URL 合法性校验
    try {
        new URL(url)
    } catch (e) {
        console.warn('无效的链接地址',e)
        return
    }

    window.open(url, '_blank')
}
</script>
<template>
    <el-table
        v-loading="tableLoading"
        :data="tableData"
        tooltip-effect="dark"
        border
        table-layout="fixed"
        fit
        :header-cell-style="{
            background: '#ECF5FF',
        }"
        size="large"
        empty-text="暂无数据"
    >
        <!-- <el-table-column type="index" label="序号" width="80" align="center" header-align="center"></el-table-column> -->
        <el-table-column
            align="center"
            header-align="center"
            v-for="(item, index) in columns"
            :key="index"
            :label="item.label"
            :prop="item.prop"
        >
            <template #default="{ row }">
                <div v-if="item.prop === 'SITEHOME'">
                    <div v-if="row.SITEHOME" class="!color-blue pointer" @click="openWebSite(row.SITEHOME)">
                        {{ row.SITEHOME }}
                    </div>
                    <div v-else>-</div>
                </div>
                <div v-else-if="item.prop === 'DETAIL'">
                    <WebsiteInformationDetailDialog :row="row" />
                </div>
                <div v-else>
                    {{ row[item.prop] || '-' }}
                </div>
            </template>
        </el-table-column>
        <el-table-column label="内容" width="90" align="center" header-align="center">
            <template #default="{ row }">
                <WebsiteInformationDetailDialog :row="row" />
            </template>
        </el-table-column>
    </el-table>
    <div class="display-flex top-bottom-center margin-top-20 justify-flex-end">
        <el-pagination
            :hide-on-single-page="true"
            v-model:currentPage="pageInfo.page"
            v-model:page-size="pageInfo.pageSize"
            layout="total, prev, pager, next"
            :total="pageInfo.total"
            @current-change="getModelData()"
        />
    </div>
</template>
<style scoped lang="scss"></style>
